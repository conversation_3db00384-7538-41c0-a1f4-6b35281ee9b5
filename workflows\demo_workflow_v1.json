{"workflow": {"id": "demo_workflow", "name": "Demo Workflow", "version": "1.0", "start": "Greeting", "allowed_actions": ["Greet user", "Say goodbye"], "prohibited_actions": ["Do not process real transactions"], "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Goodbye"}], "allowed_tools": ["TTS"]}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["TTS"]}}}}